using Microsoft.AspNetCore.Mvc;

namespace HelloApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class HelloController : ControllerBase
    {
        /// <summary>
        /// Returns a hello message with optional name parameter
        /// </summary>
        /// <param name="name">Optional name parameter</param>
        /// <param name="language">Optional language parameter (default: en)</param>
        /// <returns>Hello message</returns>
        [HttpGet]
        public ActionResult<HelloResponse> Get([FromQuery] string? name = null, [FromQuery] string language = "en")
        {
            var greeting = GetGreeting(language);
            var message = string.IsNullOrEmpty(name) 
                ? $"{greeting}, World!" 
                : $"{greeting}, {name}!";

            return Ok(new HelloResponse
            {
                Message = message,
                Name = name,
                Language = language,
                Timestamp = DateTime.UtcNow
            });
        }

        /// <summary>
        /// Returns a hello message with name from route parameter
        /// </summary>
        /// <param name="name">Name from route parameter</param>
        /// <param name="language">Optional language parameter (default: en)</param>
        /// <returns>Hello message</returns>
        [HttpGet("{name}")]
        public ActionResult<HelloResponse> GetWithName(string name, [FromQuery] string language = "en")
        {
            var greeting = GetGreeting(language);
            var message = $"{greeting}, {name}!";

            return Ok(new HelloResponse
            {
                Message = message,
                Name = name,
                Language = language,
                Timestamp = DateTime.UtcNow
            });
        }

        /// <summary>
        /// Returns a personalized hello message with multiple parameters
        /// </summary>
        /// <param name="firstName">First name</param>
        /// <param name="lastName">Last name</param>
        /// <param name="title">Optional title (Mr, Ms, Dr, etc.)</param>
        /// <param name="language">Optional language parameter (default: en)</param>
        /// <returns>Personalized hello message</returns>
        [HttpGet("personalized")]
        public ActionResult<HelloResponse> GetPersonalized(
            [FromQuery] string firstName,
            [FromQuery] string? lastName = null,
            [FromQuery] string? title = null,
            [FromQuery] string language = "en")
        {
            if (string.IsNullOrEmpty(firstName))
            {
                return BadRequest("firstName parameter is required");
            }

            var greeting = GetGreeting(language);
            var fullName = BuildFullName(title, firstName, lastName);
            var message = $"{greeting}, {fullName}!";

            return Ok(new HelloResponse
            {
                Message = message,
                Name = fullName,
                Language = language,
                Timestamp = DateTime.UtcNow
            });
        }

        private static string GetGreeting(string language)
        {
            return language.ToLower() switch
            {
                "es" or "spanish" => "Hola",
                "fr" or "french" => "Bonjour",
                "de" or "german" => "Hallo",
                "it" or "italian" => "Ciao",
                "pt" or "portuguese" => "Olá",
                "ja" or "japanese" => "こんにちは",
                "zh" or "chinese" => "你好",
                "hi" or "hindi" => "नमस्ते",
                _ => "Hello"
            };
        }

        private static string BuildFullName(string? title, string firstName, string? lastName)
        {
            var parts = new List<string>();
            
            if (!string.IsNullOrEmpty(title))
                parts.Add(title);
            
            parts.Add(firstName);
            
            if (!string.IsNullOrEmpty(lastName))
                parts.Add(lastName);

            return string.Join(" ", parts);
        }
    }

    public class HelloResponse
    {
        public string Message { get; set; } = string.Empty;
        public string? Name { get; set; }
        public string Language { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
    }
}
