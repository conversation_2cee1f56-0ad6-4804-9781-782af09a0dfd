# Hello API

A simple ASP.NET Core Web API that parses URL parameters and returns hello messages via GET HTTP operations.

## Features

- Parse URL query parameters
- Support multiple languages for greetings
- Multiple endpoint variations for different use cases
- Swagger/OpenAPI documentation
- JSON response format with timestamp

## Endpoints

### 1. Basic Hello
```
GET /api/hello
GET /api/hello?name=John
GET /api/hello?name=<PERSON>&language=es
```

### 2. Hello with Route Parameter
```
GET /api/hello/John
GET /api/hello/John?language=fr
```

### 3. Personalized Hello
```
GET /api/hello/personalized?firstName=John
GET /api/hello/personalized?firstName=John&lastName=Doe
GET /api/hello/personalized?title=Dr&firstName=<PERSON>&lastName=Smith&language=de
```

## Supported Languages

- English (en) - Default
- Spanish (es)
- French (fr)
- German (de)
- Italian (it)
- Portuguese (pt)
- Japanese (ja)
- Chinese (zh)
- Hindi (hi)

## Running the Application

1. Ensure you have .NET 8.0 SDK installed
2. Navigate to the project directory
3. Run the following commands:

```bash
dotnet restore
dotnet build
dotnet run
```

4. Open your browser and navigate to:
   - `https://localhost:7000/swagger` (HTTPS)
   - `http://localhost:5000/swagger` (HTTP)

## Example Responses

### Basic Hello
```json
{
  "message": "Hello, World!",
  "name": null,
  "language": "en",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### Hello with Name
```json
{
  "message": "Hola, Juan!",
  "name": "Juan",
  "language": "es",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### Personalized Hello
```json
{
  "message": "Bonjour, Dr John Smith!",
  "name": "Dr John Smith",
  "language": "fr",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```
