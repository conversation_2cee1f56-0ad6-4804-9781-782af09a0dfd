using Microsoft.AspNetCore.Mvc;
using HelloApi.Controllers;
using Xunit;

namespace HelloApi.Tests
{
    public class HelloControllerTests
    {
        private readonly HelloController _controller;

        public HelloControllerTests()
        {
            _controller = new HelloController();
        }

        [Fact]
        public void Get_WithoutParameters_ReturnsHelloWorld()
        {
            // Act
            var result = _controller.Get();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var response = Assert.IsType<HelloResponse>(okResult.Value);
            Assert.Equal("Hello, World!", response.Message);
            Assert.Null(response.Name);
            Assert.Equal("en", response.Language);
        }

        [Fact]
        public void Get_WithName_ReturnsHelloWithName()
        {
            // Act
            var result = _controller.Get("John");

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var response = Assert.IsType<HelloResponse>(okResult.Value);
            Assert.Equal("Hello, <PERSON>!", response.Message);
            Assert.Equal("John", response.Name);
            Assert.Equal("en", response.Language);
        }

        [Fact]
        public void Get_WithSpanishLanguage_ReturnsHolaMessage()
        {
            // Act
            var result = _controller.Get("Maria", "es");

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var response = Assert.IsType<HelloResponse>(okResult.Value);
            Assert.Equal("Hola, Maria!", response.Message);
            Assert.Equal("Maria", response.Name);
            Assert.Equal("es", response.Language);
        }

        [Fact]
        public void GetWithName_ReturnsCorrectMessage()
        {
            // Act
            var result = _controller.GetWithName("Alice", "fr");

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var response = Assert.IsType<HelloResponse>(okResult.Value);
            Assert.Equal("Bonjour, Alice!", response.Message);
            Assert.Equal("Alice", response.Name);
            Assert.Equal("fr", response.Language);
        }

        [Fact]
        public void GetPersonalized_WithAllParameters_ReturnsFullName()
        {
            // Act
            var result = _controller.GetPersonalized("John", "Doe", "Dr", "de");

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var response = Assert.IsType<HelloResponse>(okResult.Value);
            Assert.Equal("Hallo, Dr John Doe!", response.Message);
            Assert.Equal("Dr John Doe", response.Name);
            Assert.Equal("de", response.Language);
        }

        [Fact]
        public void GetPersonalized_WithoutFirstName_ReturnsBadRequest()
        {
            // Act
            var result = _controller.GetPersonalized("");

            // Assert
            Assert.IsType<BadRequestObjectResult>(result.Result);
        }

        [Fact]
        public void GetPersonalized_WithOnlyFirstName_ReturnsFirstNameOnly()
        {
            // Act
            var result = _controller.GetPersonalized("Jane");

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var response = Assert.IsType<HelloResponse>(okResult.Value);
            Assert.Equal("Hello, Jane!", response.Message);
            Assert.Equal("Jane", response.Name);
        }
    }
}
