using System.Text.Json;

namespace HelloApi.Examples
{
    public class HttpClientExample
    {
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl;

        public HttpClientExample(string baseUrl = "https://localhost:7000")
        {
            _httpClient = new HttpClient();
            _baseUrl = baseUrl;
        }

        /// <summary>
        /// Example of calling the basic hello endpoint
        /// </summary>
        public async Task<HelloResponse?> GetBasicHelloAsync(string? name = null, string? language = null)
        {
            var queryParams = new List<string>();
            
            if (!string.IsNullOrEmpty(name))
                queryParams.Add($"name={Uri.EscapeDataString(name)}");
            
            if (!string.IsNullOrEmpty(language))
                queryParams.Add($"language={Uri.EscapeDataString(language)}");

            var queryString = queryParams.Count > 0 ? "?" + string.Join("&", queryParams) : "";
            var url = $"{_baseUrl}/api/hello{queryString}";

            var response = await _httpClient.GetAsync(url);
            response.EnsureSuccessStatusCode();

            var json = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<HelloResponse>(json, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });
        }

        /// <summary>
        /// Example of calling the hello endpoint with route parameter
        /// </summary>
        public async Task<HelloResponse?> GetHelloWithRouteAsync(string name, string? language = null)
        {
            var queryString = !string.IsNullOrEmpty(language) ? $"?language={Uri.EscapeDataString(language)}" : "";
            var url = $"{_baseUrl}/api/hello/{Uri.EscapeDataString(name)}{queryString}";

            var response = await _httpClient.GetAsync(url);
            response.EnsureSuccessStatusCode();

            var json = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<HelloResponse>(json, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });
        }

        /// <summary>
        /// Example of calling the personalized hello endpoint
        /// </summary>
        public async Task<HelloResponse?> GetPersonalizedHelloAsync(
            string firstName, 
            string? lastName = null, 
            string? title = null, 
            string? language = null)
        {
            var queryParams = new List<string>
            {
                $"firstName={Uri.EscapeDataString(firstName)}"
            };

            if (!string.IsNullOrEmpty(lastName))
                queryParams.Add($"lastName={Uri.EscapeDataString(lastName)}");
            
            if (!string.IsNullOrEmpty(title))
                queryParams.Add($"title={Uri.EscapeDataString(title)}");
            
            if (!string.IsNullOrEmpty(language))
                queryParams.Add($"language={Uri.EscapeDataString(language)}");

            var queryString = "?" + string.Join("&", queryParams);
            var url = $"{_baseUrl}/api/hello/personalized{queryString}";

            var response = await _httpClient.GetAsync(url);
            response.EnsureSuccessStatusCode();

            var json = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<HelloResponse>(json, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }

    /// <summary>
    /// Console application to demonstrate the HTTP client usage
    /// </summary>
    public class Program
    {
        public static async Task Main(string[] args)
        {
            var client = new HttpClientExample();

            try
            {
                Console.WriteLine("=== Hello API Client Examples ===\n");

                // Example 1: Basic hello
                Console.WriteLine("1. Basic Hello:");
                var response1 = await client.GetBasicHelloAsync();
                Console.WriteLine($"   Response: {response1?.Message}");

                // Example 2: Hello with name
                Console.WriteLine("\n2. Hello with Name:");
                var response2 = await client.GetBasicHelloAsync("Alice");
                Console.WriteLine($"   Response: {response2?.Message}");

                // Example 3: Hello with name and language
                Console.WriteLine("\n3. Hello with Name and Language:");
                var response3 = await client.GetBasicHelloAsync("Carlos", "es");
                Console.WriteLine($"   Response: {response3?.Message}");

                // Example 4: Hello with route parameter
                Console.WriteLine("\n4. Hello with Route Parameter:");
                var response4 = await client.GetHelloWithRouteAsync("Marie", "fr");
                Console.WriteLine($"   Response: {response4?.Message}");

                // Example 5: Personalized hello
                Console.WriteLine("\n5. Personalized Hello:");
                var response5 = await client.GetPersonalizedHelloAsync("John", "Smith", "Dr", "de");
                Console.WriteLine($"   Response: {response5?.Message}");

                Console.WriteLine("\n=== All examples completed successfully! ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                Console.WriteLine("Make sure the API is running on https://localhost:7000");
            }
            finally
            {
                client.Dispose();
            }
        }
    }
}
